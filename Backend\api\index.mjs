/**
 * @fileoverview Unified AWS Lambda handler for all form submissions
 * This single entry point handler routes different form submission types to their respective handlers.
 * Required for AWS Lambda functions which can only have one handler function specified in configuration.
 *
 * Supported routes:
 * - POST /contact-us - Contact form submissions
 * - POST /ai-readiness - AI readiness assessment form submissions
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { handler as contactUs<PERSON><PERSON><PERSON> } from "./contact-us/index.mjs";
import { handler as aiReadinessHandler } from "./ai-readiness/index.mjs";

/**
 * Unified AWS Lambda handler that routes requests to appropriate form handlers
 *
 * @async
 * @function handler
 * @param {Object} event - AWS Lambda event object
 * @param {string} event.path - Request path for routing
 * @param {string} event.httpMethod - HTTP method (should be POST for form submissions)
 * @param {string} event.body - JSON string containing form data
 * @param {Object} event.headers - HTTP headers from the request
 * @param {Object} context - AWS Lambda context object
 * @returns {Promise<Object>} Lambda response object with statusCode and body
 *
 * @example
 * // Contact form request:
 * {
 *   "path": "/contact-us",
 *   "httpMethod": "POST",
 *   "body": "{\"firstName\":\"John\",\"lastName\":\"Doe\",...}"
 * }
 *
 * @example
 * // AI readiness assessment request:
 * {
 *   "path": "/ai-readiness",
 *   "httpMethod": "POST",
 *   "body": "{\"firstName\":\"Jane\",\"companyName\":\"Tech Corp\",...}"
 * }
 */
export async function handler(event, context) {
  try {
    const route = event?.path;
    const method = event?.httpMethod;

    console.log(`Received ${method} request for route: ${route}`);

    // Route contact form submissions
    if (
      (route === "/contact-us" || route === "contact-us") &&
      method === "POST"
    ) {
      console.log("Routing to contact-us handler");
      return await contactUsHandler(event, context);
    }

    // Route AI readiness assessment submissions
    if (
      (route === "/ai-readiness" || route === "ai-readiness") &&
      method === "POST"
    ) {
      console.log("Routing to ai-readiness handler");
      return await aiReadinessHandler(event, context);
    }

    // Handle unsupported routes
    console.warn(`Unsupported route: ${method} ${route}`);
    return {
      statusCode: 404,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
      },
      body: JSON.stringify({
        message: "Route not found",
        supportedRoutes: ["POST /contact-us", "POST /ai-readiness"],
      }),
    };
  } catch (error) {
    console.error("Error in unified handler:", error);
    return {
      statusCode: 500,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
      body: JSON.stringify({
        message: "Internal server error in routing",
        error: error.message || error,
      }),
    };
  }
}
