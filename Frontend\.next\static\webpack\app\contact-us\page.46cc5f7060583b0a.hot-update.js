/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact-us/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CAwardsCarousel%5CAwardsCarousel.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CContactUs%5CContactUs.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CForm%5CForm.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cvariables.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbreakpoints.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Ctypography.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeading%5CHeading.module.css&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CAwardsCarousel%5CAwardsCarousel.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CContactUs%5CContactUs.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CForm%5CForm.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cvariables.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbreakpoints.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Ctypography.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeading%5CHeading.module.css&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(app-pages-browser)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AwardsCarousel/AwardsCarousel.tsx */ \"(app-pages-browser)/./src/components/AwardsCarousel/AwardsCarousel.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./src/components/ContactUs/ContactUs.module.css */ \"(app-pages-browser)/./src/components/ContactUs/ContactUs.module.css\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Form/Form.tsx */ \"(app-pages-browser)/./src/components/Form/Form.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./src/styles/variables.module.css */ \"(app-pages-browser)/./src/styles/variables.module.css\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./src/styles/breakpoints.module.css */ \"(app-pages-browser)/./src/styles/breakpoints.module.css\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./src/styles/typography.module.css */ \"(app-pages-browser)/./src/styles/typography.module.css\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./src/components/Heading/Heading.module.css */ \"(app-pages-browser)/./src/components/Heading/Heading.module.css\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CAwardsCarousel%5CAwardsCarousel.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CContactUs%5CContactUs.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CForm%5CForm.tsx&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cvariables.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Cbreakpoints.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Cstyles%5Ctypography.module.css&modules=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Ccomponents%5CHeading%5CHeading.module.css&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useForm.ts":
/*!******************************!*\
  !*** ./src/hooks/useForm.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useForm; }\n/* harmony export */ });\n/* harmony import */ var common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! common/getUserIpData */ \"(app-pages-browser)/./src/common/getUserIpData.ts\");\n/* harmony import */ var common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! common/getUserTrackingData */ \"(app-pages-browser)/./src/common/getUserTrackingData.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction useForm(initialValues, initialErrors) {\n    let variant = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"default\", source = arguments.length > 3 ? arguments[3] : void 0, pdf_link = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : \"\";\n    const [values, setValues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialValues);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialErrors);\n    const [errorMessages, setErrorMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        empty: \"\",\n        invalid: \"\"\n    });\n    // Define required fields dynamically based on variant\n    const requiredFields = variant === \"caseStudy\" ? [\n        \"firstName\",\n        \"emailAddress\",\n        \"phoneNumber\"\n    ] : [\n        \"firstName\",\n        \"lastName\",\n        \"emailAddress\",\n        \"phoneNumber\",\n        \"consent\"\n    ];\n    const constructErrorMessages = (newErrors)=>{\n        var _newErrors_emailAddress, _newErrors_phoneNumber, _newErrors_emailAddress1, _newErrors_phoneNumber1;\n        const newErrorMessages = {\n            ...errorMessages\n        };\n        // Check only required fields based on variant\n        if (requiredFields.some((field)=>{\n            var _newErrors_field;\n            return (_newErrors_field = newErrors[field]) === null || _newErrors_field === void 0 ? void 0 : _newErrors_field.empty;\n        })) {\n            newErrorMessages.empty = \"Please fill the highlighted fields\";\n        } else {\n            newErrorMessages.empty = \"\";\n        }\n        if (((_newErrors_emailAddress = newErrors.emailAddress) === null || _newErrors_emailAddress === void 0 ? void 0 : _newErrors_emailAddress.invalid) && ((_newErrors_phoneNumber = newErrors.phoneNumber) === null || _newErrors_phoneNumber === void 0 ? void 0 : _newErrors_phoneNumber.invalid)) {\n            newErrorMessages.invalid = \"Please enter valid Email ID and Phone Number\";\n        } else if ((_newErrors_emailAddress1 = newErrors.emailAddress) === null || _newErrors_emailAddress1 === void 0 ? void 0 : _newErrors_emailAddress1.invalid) {\n            newErrorMessages.invalid = \"Please enter a valid Email ID\";\n        } else if ((_newErrors_phoneNumber1 = newErrors.phoneNumber) === null || _newErrors_phoneNumber1 === void 0 ? void 0 : _newErrors_phoneNumber1.invalid) {\n            newErrorMessages.invalid = \"Please enter a valid Phone Number\";\n        } else {\n            newErrorMessages.invalid = \"\";\n        }\n        setErrorMessages(newErrorMessages);\n    };\n    const validateField = (name, value)=>{\n        const newErrors = {\n            ...errors\n        };\n        if (!value) {\n            newErrors[name] = {\n                empty: true,\n                invalid: false\n            };\n        } else if (name === \"emailAddress\" && !/\\S+@\\S+\\.\\S+/.test(value)) {\n            newErrors[name] = {\n                empty: false,\n                invalid: true\n            };\n        } else if (name === \"phoneNumber\" && !/.{6,}/.test(value)) {\n            newErrors[name] = {\n                empty: false,\n                invalid: true\n            };\n        } else {\n            newErrors[name] = initialErrors[name];\n        }\n        setErrors(newErrors);\n        constructErrorMessages(newErrors);\n    };\n    const handleBlur = (param)=>{\n        let { name, value } = param;\n        const newValues = {\n            ...values\n        };\n        if (typeof value === \"string\") {\n            newValues[name] = value.trim();\n        }\n        setValues(newValues);\n        if (name in errors) {\n            validateField(name, value);\n        }\n    };\n    const handleChange = (param)=>{\n        let { name, value, type = \"\", checked = false } = param;\n        value = type === \"checkbox\" ? checked : value;\n        const newValues = {\n            ...values\n        };\n        if (name === \"firstName\" || name === \"lastName\") {\n            newValues[name] = value.replace(/[^a-zA-Z0-9 ]/g, \"\").trimStart();\n        } else if (name === \"emailAddress\") {\n            newValues[name] = value.replace(\" \", \"\");\n        } else {\n            newValues[name] = value;\n        }\n        setValues(newValues);\n        if (name in errors) {\n            validateField(name, value);\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {\n            ...errors\n        };\n        // Ensure each field has an empty object if it doesn't exis\n        requiredFields.forEach((field)=>{\n            if (!values[field]) {\n                newErrors[field] = {\n                    empty: true,\n                    invalid: false\n                }; // Explicitly set both properties\n            }\n        });\n        setErrors(newErrors);\n        constructErrorMessages(newErrors);\n        return !Object.values(newErrors).some((error)=>error.empty || error.invalid);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (validateForm()) {\n            try {\n                const userIPData = await (0,common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n                const userTrackingData = await (0,common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const formData = {\n                    firstName: values.firstName || \"\",\n                    lastName: values.lastName || \"\",\n                    emailAddress: values.emailAddress || \"\",\n                    phoneNumber: values.phoneNumber || \"\",\n                    howDidYouHearAboutUs: values.howDidYouHearAboutUs || \"\",\n                    companyName: values.companyName || \"\",\n                    howCanWeHelpYou: values.howCanWeHelpYou || \"\",\n                    utm_campaign: userTrackingData.utm_campaign || \"\",\n                    utm_medium: userTrackingData.utm_medium || \"\",\n                    utm_source: userTrackingData.utm_source || \"\",\n                    ip_address: userIPData.ipAddress || \"\",\n                    ga_4_userid: userTrackingData.ga_client_id || \"\",\n                    city: userIPData.location.city || \"\",\n                    country: userIPData.location.country || \"\",\n                    secondary_source: source || \"\",\n                    clarity: userTrackingData.clarity || \"\",\n                    url: window.location.href || \"\",\n                    referrer: userTrackingData.referrer || \"\",\n                    consent: values.consent || false\n                };\n                const response = await fetch(\"\".concat(\"https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev\", \"/contact-us\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"x-api-key\": \"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4\" || 0\n                    },\n                    body: JSON.stringify(formData)\n                });\n                console.log(\"Form Data\", response);\n                if (response.ok) {\n                    console.log(\"inside if\");\n                    if ((source === \"CaseStudy\" || source === \"eBooks\" || source === \"whitePapers\") && pdf_link) {\n                        window.open(pdf_link, \"_blank\");\n                    }\n                    window.location.href = \"/thank-you/\";\n                } else {\n                    console.error(\"Error submitting form:\", await response.json());\n                }\n            } catch (error) {\n                console.error(\"Error in form submission:\", error);\n            }\n            setValues(initialValues);\n            setErrors(initialErrors);\n        }\n    };\n    const handleSubmitAIReadiness = async (data, newResult, handleVisibleSection)=>{\n        if (validateForm()) {\n            try {\n                const userIPData = await (0,common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n                const userTrackingData = await (0,common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const formData = {\n                    firstName: values.firstName || \"\",\n                    lastName: values.lastName || \"\",\n                    emailAddress: values.emailAddress || \"\",\n                    phoneNumber: values.phoneNumber || \"\",\n                    companyName: values.companyName || \"\",\n                    utm_campaign: userTrackingData.utm_campaign || \"\",\n                    utm_medium: userTrackingData.utm_medium || \"\",\n                    utm_source: userTrackingData.utm_source || \"\",\n                    ip_address: userIPData.ipAddress || \"\",\n                    ga_4_userid: userTrackingData.ga_client_id || \"\",\n                    city: userIPData.location.city || \"\",\n                    country: userIPData.location.country || \"\",\n                    secondary_source: source || \"\",\n                    clarity: userTrackingData.clarity || \"\",\n                    url: window.location.href || \"\",\n                    referrer: userTrackingData.referrer || \"\",\n                    consent: values.consent || false,\n                    do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_: data[0][0][0] || \"\",\n                    how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_: data[0][1][0] || \"\",\n                    do_you_have_budget_allocated_for_your_ai_project_: data[0][2][0] || \"\",\n                    do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_: data[1][0][0] || \"\",\n                    which_of_the_below_db_tools_do_you_currently_use_: data[1][1][0] || \"\",\n                    is_the_relevant_data_for_the_ai_project_available_and_accessible_: data[1][2][0] || \"\",\n                    do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__: data[1][3][0] || \"\",\n                    how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib: data[1][4][0] || \"\",\n                    does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_: data[2][0][0] || \"\",\n                    do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_: data[3][0][0] || \"\",\n                    do_you_have_risk_management_strategies_in_place_for_the_ai_project_: data[3][1][0] || \"\",\n                    do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions: data[4][0][0] || \"\",\n                    strategy___leadership: newResult[0] || \"\",\n                    data_readiness___infrastructure: newResult[1] || \"\",\n                    talent___skills: newResult[2] || \"\",\n                    execution___monitoring: newResult[3] || \"\",\n                    impact_evaliation: newResult[4] || \"\",\n                    average_of_all_score: newResult[\"final\"] || \"\"\n                };\n                const response = await fetch(\"\".concat(\"https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev\", \"/ai-readiness\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"x-api-key\": \"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4\" || 0\n                    },\n                    body: JSON.stringify(formData)\n                });\n                if (response.ok) {\n                    // if (\n                    //   (source === 'CaseStudy' ||\n                    //     source === 'eBooks' ||\n                    //     source === 'whitePapers') &&\n                    //   pdf_link\n                    // ) {\n                    //   window.open(pdf_link, '_blank');\n                    // }\n                    // window.location.href = '/thank-you/';\n                    handleVisibleSection(data.length);\n                } else {\n                    console.error(\"Error submitting form:\", await response.json());\n                }\n            } catch (error) {\n                console.error(\"Error in form submission:\", error);\n            }\n            setValues(initialValues);\n            setErrors(initialErrors);\n        }\n    };\n    return {\n        values,\n        errors,\n        errorMessages,\n        handleChange,\n        handleBlur,\n        handleSubmit,\n        handleSubmitAIReadiness\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useForm.ts\n"));

/***/ })

});