/**
 * @fileoverview Test script for the unified Lambda handler
 * This script tests the routing functionality of the unified handler
 * without actually sending data to external services.
 */

import { handler } from './index.mjs';

// Mock event for contact-us form
const contactUsEvent = {
  path: '/contact-us',
  httpMethod: 'POST',
  body: JSON.stringify({
    firstName: 'John',
    lastName: 'Doe',
    emailAddress: '<EMAIL>',
    phoneNumber: '+**********',
    companyName: 'Test Corp',
    howCanWeHelpYou: 'Testing the unified handler',
    howDidYouHearAboutUs: 'Testing',
    consent: true,
    url: 'https://example.com/contact',
    secondary_source: 'Test'
  }),
  headers: {
    'Content-Type': 'application/json'
  }
};

// Mock event for AI readiness assessment
const aiReadinessEvent = {
  path: '/ai-readiness-assessment',
  httpMethod: 'POST',
  body: JSON.stringify({
    firstName: 'Jane',
    lastName: 'Smith',
    emailAddress: '<EMAIL>',
    companyName: 'AI Corp',
    strategy___leadership: '85',
    talent___skills: '70',
    data_readiness___infrastructure: '75',
    impact_evaliation: '80',
    execution___monitoring: '65',
    average_of_all_score: '75',
    secondary_source: 'Test'
  }),
  headers: {
    'Content-Type': 'application/json'
  }
};

// Mock event for unsupported route
const unsupportedEvent = {
  path: '/unsupported-route',
  httpMethod: 'POST',
  body: JSON.stringify({}),
  headers: {
    'Content-Type': 'application/json'
  }
};

// Mock context
const mockContext = {
  functionName: 'test-function',
  functionVersion: '1.0.0'
};

async function runTests() {
  console.log('🧪 Testing Unified Lambda Handler\n');

  try {
    // Test 1: Contact Us Route
    console.log('1️⃣ Testing Contact Us route...');
    console.log('Route: POST /contact-us');
    
    // Note: This will fail because we don't have SSM configured in test environment
    // But it should at least route correctly and show the routing logic works
    try {
      const contactResult = await handler(contactUsEvent, mockContext);
      console.log('✅ Contact Us routing successful');
      console.log('Status Code:', contactResult.statusCode);
    } catch (error) {
      console.log('⚠️ Contact Us handler error (expected in test environment):', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: AI Readiness Route
    console.log('2️⃣ Testing AI Readiness route...');
    console.log('Route: POST /ai-readiness-assessment');
    
    try {
      const aiResult = await handler(aiReadinessEvent, mockContext);
      console.log('✅ AI Readiness routing successful');
      console.log('Status Code:', aiResult.statusCode);
    } catch (error) {
      console.log('⚠️ AI Readiness handler error (expected in test environment):', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Unsupported Route
    console.log('3️⃣ Testing unsupported route...');
    console.log('Route: POST /unsupported-route');
    
    const unsupportedResult = await handler(unsupportedEvent, mockContext);
    console.log('✅ Unsupported route handled correctly');
    console.log('Status Code:', unsupportedResult.statusCode);
    console.log('Response:', JSON.parse(unsupportedResult.body));

    console.log('\n🎉 All routing tests completed!');
    console.log('\nNote: Individual handler errors are expected in test environment');
    console.log('due to missing SSM configuration. The important thing is that');
    console.log('routing works correctly and unsupported routes return 404.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the tests
runTests();
