"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useForm.ts":
/*!******************************!*\
  !*** ./src/hooks/useForm.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useForm; }\n/* harmony export */ });\n/* harmony import */ var common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! common/getUserIpData */ \"(app-pages-browser)/./src/common/getUserIpData.ts\");\n/* harmony import */ var common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! common/getUserTrackingData */ \"(app-pages-browser)/./src/common/getUserTrackingData.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction useForm(initialValues, initialErrors) {\n    let variant = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"default\", source = arguments.length > 3 ? arguments[3] : void 0, pdf_link = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : \"\";\n    const [values, setValues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialValues);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialErrors);\n    const [errorMessages, setErrorMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        empty: \"\",\n        invalid: \"\"\n    });\n    // Define required fields dynamically based on variant\n    const requiredFields = variant === \"caseStudy\" ? [\n        \"firstName\",\n        \"emailAddress\",\n        \"phoneNumber\"\n    ] : [\n        \"firstName\",\n        \"lastName\",\n        \"emailAddress\",\n        \"phoneNumber\",\n        \"consent\"\n    ];\n    const constructErrorMessages = (newErrors)=>{\n        var _newErrors_emailAddress, _newErrors_phoneNumber, _newErrors_emailAddress1, _newErrors_phoneNumber1;\n        const newErrorMessages = {\n            ...errorMessages\n        };\n        // Check only required fields based on variant\n        if (requiredFields.some((field)=>{\n            var _newErrors_field;\n            return (_newErrors_field = newErrors[field]) === null || _newErrors_field === void 0 ? void 0 : _newErrors_field.empty;\n        })) {\n            newErrorMessages.empty = \"Please fill the highlighted fields\";\n        } else {\n            newErrorMessages.empty = \"\";\n        }\n        if (((_newErrors_emailAddress = newErrors.emailAddress) === null || _newErrors_emailAddress === void 0 ? void 0 : _newErrors_emailAddress.invalid) && ((_newErrors_phoneNumber = newErrors.phoneNumber) === null || _newErrors_phoneNumber === void 0 ? void 0 : _newErrors_phoneNumber.invalid)) {\n            newErrorMessages.invalid = \"Please enter valid Email ID and Phone Number\";\n        } else if ((_newErrors_emailAddress1 = newErrors.emailAddress) === null || _newErrors_emailAddress1 === void 0 ? void 0 : _newErrors_emailAddress1.invalid) {\n            newErrorMessages.invalid = \"Please enter a valid Email ID\";\n        } else if ((_newErrors_phoneNumber1 = newErrors.phoneNumber) === null || _newErrors_phoneNumber1 === void 0 ? void 0 : _newErrors_phoneNumber1.invalid) {\n            newErrorMessages.invalid = \"Please enter a valid Phone Number\";\n        } else {\n            newErrorMessages.invalid = \"\";\n        }\n        setErrorMessages(newErrorMessages);\n    };\n    const validateField = (name, value)=>{\n        const newErrors = {\n            ...errors\n        };\n        if (!value) {\n            newErrors[name] = {\n                empty: true,\n                invalid: false\n            };\n        } else if (name === \"emailAddress\" && !/\\S+@\\S+\\.\\S+/.test(value)) {\n            newErrors[name] = {\n                empty: false,\n                invalid: true\n            };\n        } else if (name === \"phoneNumber\" && !/.{6,}/.test(value)) {\n            newErrors[name] = {\n                empty: false,\n                invalid: true\n            };\n        } else {\n            newErrors[name] = initialErrors[name];\n        }\n        setErrors(newErrors);\n        constructErrorMessages(newErrors);\n    };\n    const handleBlur = (param)=>{\n        let { name, value } = param;\n        const newValues = {\n            ...values\n        };\n        if (typeof value === \"string\") {\n            newValues[name] = value.trim();\n        }\n        setValues(newValues);\n        if (name in errors) {\n            validateField(name, value);\n        }\n    };\n    const handleChange = (param)=>{\n        let { name, value, type = \"\", checked = false } = param;\n        value = type === \"checkbox\" ? checked : value;\n        const newValues = {\n            ...values\n        };\n        if (name === \"firstName\" || name === \"lastName\") {\n            newValues[name] = value.replace(/[^a-zA-Z0-9 ]/g, \"\").trimStart();\n        } else if (name === \"emailAddress\") {\n            newValues[name] = value.replace(\" \", \"\");\n        } else {\n            newValues[name] = value;\n        }\n        setValues(newValues);\n        if (name in errors) {\n            validateField(name, value);\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {\n            ...errors\n        };\n        // Ensure each field has an empty object if it doesn't exis\n        requiredFields.forEach((field)=>{\n            if (!values[field]) {\n                newErrors[field] = {\n                    empty: true,\n                    invalid: false\n                }; // Explicitly set both properties\n            }\n        });\n        setErrors(newErrors);\n        constructErrorMessages(newErrors);\n        return !Object.values(newErrors).some((error)=>error.empty || error.invalid);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (validateForm()) {\n            try {\n                const userIPData = await (0,common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n                const userTrackingData = await (0,common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const formData = {\n                    firstName: values.firstName || \"\",\n                    lastName: values.lastName || \"\",\n                    emailAddress: values.emailAddress || \"\",\n                    phoneNumber: values.phoneNumber || \"\",\n                    howDidYouHearAboutUs: values.howDidYouHearAboutUs || \"\",\n                    companyName: values.companyName || \"\",\n                    howCanWeHelpYou: values.howCanWeHelpYou || \"\",\n                    utm_campaign: userTrackingData.utm_campaign || \"\",\n                    utm_medium: userTrackingData.utm_medium || \"\",\n                    utm_source: userTrackingData.utm_source || \"\",\n                    ip_address: userIPData.ipAddress || \"\",\n                    ga_4_userid: userTrackingData.ga_client_id || \"\",\n                    city: userIPData.location.city || \"\",\n                    country: userIPData.location.country || \"\",\n                    secondary_source: source || \"\",\n                    clarity: userTrackingData.clarity || \"\",\n                    url: window.location.href || \"\",\n                    referrer: userTrackingData.referrer || \"\",\n                    consent: values.consent || false\n                };\n                const response = await fetch(\"\".concat(\"https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev\", \"/contact-us\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"x-api-key\": \"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4\" || 0\n                    },\n                    body: JSON.stringify(formData)\n                });\n                console.log(\"Form Data\", response);\n                if (response.ok) {\n                    console.log(\"inside if\");\n                    if ((source === \"CaseStudy\" || source === \"eBooks\" || source === \"whitePapers\") && pdf_link) {\n                        window.open(pdf_link, \"_blank\");\n                    }\n                    window.location.href = \"/thank-you/\";\n                } else {\n                    console.log(\"inside else\");\n                    console.error(\"Error submitting form:\", await response.json());\n                }\n            } catch (error) {\n                console.error(\"Error in form submission:\", error);\n            }\n            setValues(initialValues);\n            setErrors(initialErrors);\n        }\n    };\n    const handleSubmitAIReadiness = async (data, newResult, handleVisibleSection)=>{\n        if (validateForm()) {\n            try {\n                const userIPData = await (0,common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n                const userTrackingData = await (0,common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const formData = {\n                    firstName: values.firstName || \"\",\n                    lastName: values.lastName || \"\",\n                    emailAddress: values.emailAddress || \"\",\n                    phoneNumber: values.phoneNumber || \"\",\n                    companyName: values.companyName || \"\",\n                    utm_campaign: userTrackingData.utm_campaign || \"\",\n                    utm_medium: userTrackingData.utm_medium || \"\",\n                    utm_source: userTrackingData.utm_source || \"\",\n                    ip_address: userIPData.ipAddress || \"\",\n                    ga_4_userid: userTrackingData.ga_client_id || \"\",\n                    city: userIPData.location.city || \"\",\n                    country: userIPData.location.country || \"\",\n                    secondary_source: source || \"\",\n                    clarity: userTrackingData.clarity || \"\",\n                    url: window.location.href || \"\",\n                    referrer: userTrackingData.referrer || \"\",\n                    consent: values.consent || false,\n                    do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_: data[0][0][0] || \"\",\n                    how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_: data[0][1][0] || \"\",\n                    do_you_have_budget_allocated_for_your_ai_project_: data[0][2][0] || \"\",\n                    do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_: data[1][0][0] || \"\",\n                    which_of_the_below_db_tools_do_you_currently_use_: data[1][1][0] || \"\",\n                    is_the_relevant_data_for_the_ai_project_available_and_accessible_: data[1][2][0] || \"\",\n                    do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__: data[1][3][0] || \"\",\n                    how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib: data[1][4][0] || \"\",\n                    does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_: data[2][0][0] || \"\",\n                    do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_: data[3][0][0] || \"\",\n                    do_you_have_risk_management_strategies_in_place_for_the_ai_project_: data[3][1][0] || \"\",\n                    do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions: data[4][0][0] || \"\",\n                    strategy___leadership: newResult[0] || \"\",\n                    data_readiness___infrastructure: newResult[1] || \"\",\n                    talent___skills: newResult[2] || \"\",\n                    execution___monitoring: newResult[3] || \"\",\n                    impact_evaliation: newResult[4] || \"\",\n                    average_of_all_score: newResult[\"final\"] || \"\"\n                };\n                const response = await fetch(\"\".concat(\"https://f8hlswzehk.execute-api.ap-south-1.amazonaws.com/dev\", \"/ai-readiness\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"x-api-key\": \"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4\" || 0\n                    },\n                    body: JSON.stringify(formData)\n                });\n                if (response.ok) {\n                    // if (\n                    //   (source === 'CaseStudy' ||\n                    //     source === 'eBooks' ||\n                    //     source === 'whitePapers') &&\n                    //   pdf_link\n                    // ) {\n                    //   window.open(pdf_link, '_blank');\n                    // }\n                    // window.location.href = '/thank-you/';\n                    handleVisibleSection(data.length);\n                } else {\n                    console.error(\"Error submitting form:\", await response.json());\n                }\n            } catch (error) {\n                console.error(\"Error in form submission:\", error);\n            }\n            setValues(initialValues);\n            setErrors(initialErrors);\n        }\n    };\n    return {\n        values,\n        errors,\n        errorMessages,\n        handleChange,\n        handleBlur,\n        handleSubmit,\n        handleSubmitAIReadiness\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useForm.ts\n"));

/***/ })

});